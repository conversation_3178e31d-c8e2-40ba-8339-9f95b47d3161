require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

# Minimum iOS version for React Native 0.73.9
platform :ios, '13.4'

target 'ReactNativeBridgeIos' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  # RNGestureHandler is now auto-linked, no need for manual pod declaration

  target 'ReactNativeBridgeIosTests' do
    inherit! :complete
    # Pods for testing
  end

  # Flipper is deprecated in React Native 0.73+
  # Use React Native DevTools or other debugging tools instead

  post_install do |installer|
    # React Native post install hook for React Native 0.73.9
    react_native_post_install(
      installer,
      config[:reactNativePath],
      # Set `mac_catalyst_enabled` to `true` in order to apply patches
      # necessary for Mac Catalyst builds
      :mac_catalyst_enabled => false
    )

    installer.generated_projects.each do |project|
      project.targets.each do |target|
          target.build_configurations.each do |config|
              config.build_settings["DEVELOPMENT_TEAM"] = "WF47643S5X"
              config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
           end
      end
    end
  end
end
